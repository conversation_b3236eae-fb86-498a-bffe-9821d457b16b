#!/usr/bin/env python3
"""
Test the MCP server via stdio transport
"""

import subprocess
import json
import sys


def test_mcp_stdio():
    """Test the MCP server via stdio."""
    print("🔌 Testing MCP Server via STDIO")
    print("=" * 50)
    
    try:
        # Start the MCP server process
        process = subprocess.Popen(
            [sys.executable, "grid_game_server.py", "--stdio"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd="."
        )
        
        # Send initialization request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print("📤 Sending initialization request...")
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        # Read response
        response_line = process.stdout.readline()
        if response_line:
            print("📥 Received response:")
            try:
                response = json.loads(response_line.strip())
                print(json.dumps(response, indent=2))
                
                if "result" in response:
                    print("✅ Server initialized successfully!")
                    
                    # Test listing tools
                    tools_request = {
                        "jsonrpc": "2.0",
                        "id": 2,
                        "method": "tools/list"
                    }
                    
                    print("\n📤 Requesting tools list...")
                    process.stdin.write(json.dumps(tools_request) + "\n")
                    process.stdin.flush()
                    
                    tools_response = process.stdout.readline()
                    if tools_response:
                        print("📥 Tools response:")
                        tools_data = json.loads(tools_response.strip())
                        print(json.dumps(tools_data, indent=2))
                        
                        if "result" in tools_data and "tools" in tools_data["result"]:
                            tools = tools_data["result"]["tools"]
                            print(f"\n✅ Found {len(tools)} tools:")
                            for tool in tools:
                                print(f"  - {tool['name']}")
                        
                else:
                    print("❌ Initialization failed")
                    print(response)
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw response: {response_line}")
        else:
            print("❌ No response received")
        
        # Clean up
        process.terminate()
        process.wait(timeout=5)
        
    except Exception as e:
        print(f"❌ Error testing stdio: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_mcp_stdio()
