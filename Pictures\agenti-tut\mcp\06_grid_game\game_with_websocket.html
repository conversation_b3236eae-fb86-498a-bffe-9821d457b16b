<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Grid Bot Adventure - MCP Controlled</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: 'Exo 2', sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e0e6ed;
            min-height: 100vh;
            overflow-x: auto;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-size: 3em;
            font-weight: 900;
            margin: 20px 0 10px 0;
            background: linear-gradient(45deg, #00d4ff, #ff0080, #00ff88);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: rainbow 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        @keyframes rainbow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .connection-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }

        .connected {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid rgba(0, 255, 0, 0.5);
            color: #00ff88;
        }

        .disconnected {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.5);
            color: #ff4444;
        }

        .game-container {
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }

        .game-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .game-canvas {
            border: 3px solid transparent;
            border-radius: 15px;
            box-shadow:
                0 0 30px rgba(0, 212, 255, 0.3),
                0 0 60px rgba(255, 0, 128, 0.2),
                inset 0 0 30px rgba(0, 255, 136, 0.1);
            background: linear-gradient(145deg, #1e2a3a, #2c3e50);
            position: relative;
            overflow: hidden;
        }

        .game-info {
            flex: 1;
            background: linear-gradient(145deg,
                rgba(30, 42, 58, 0.9),
                rgba(44, 62, 80, 0.8)
            );
            padding: 25px;
            border-radius: 15px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.1);
        }

        .info-section {
            margin-bottom: 25px;
            position: relative;
        }

        .info-section h3 {
            margin: 0 0 15px 0;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.4em;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 12px 15px;
            background: linear-gradient(90deg,
                rgba(0, 212, 255, 0.1),
                rgba(255, 0, 128, 0.05)
            );
            border-radius: 8px;
            border-left: 3px solid #00d4ff;
            transition: all 0.3s ease;
        }

        .status-value {
            font-weight: 600;
            color: #00ff88;
            text-shadow: 0 0 5px rgba(0, 255, 136, 0.5);
        }

        .mcp-controls {
            background: linear-gradient(145deg,
                rgba(0, 212, 255, 0.1),
                rgba(255, 0, 128, 0.05)
            );
            padding: 20px;
            border-radius: 12px;
            margin-top: 15px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .btn {
            background: linear-gradient(45deg, #ff0080, #ff4060, #ff6040);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Exo 2', sans-serif;
            font-size: 0.9em;
            font-weight: 600;
            margin: 4px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 0, 128, 0.4);
        }

        .btn.mcp-btn {
            background: linear-gradient(45deg, #00d4ff, #0080ff, #4060ff);
        }

        .btn.mcp-btn:hover {
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
        }

        .game-log {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 10px;
        }

        .command-log {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            padding: 10px;
            border-radius: 5px;
            max-height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.8em;
            margin-top: 10px;
        }

        .legend {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }

        .legend-emoji {
            font-size: 1.5em;
            margin-right: 10px;
            width: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Grid Bot Adventure</h1>
            <p><strong>Real-time MCP Controlled Game</strong></p>
            <div id="connection-status" class="connection-status disconnected">
                🔌 Connecting to MCP Bridge...
            </div>
        </div>
        
        <div class="game-container">
            <div class="game-area">
                <div id="game-canvas-container">
                    <!-- P5.js canvas will be inserted here -->
                </div>
            </div>

            <div class="game-info">
                <div class="info-section">
                    <h3>🎯 Game Status</h3>
                    <div class="status-item">
                        <span>💰 Score:</span>
                        <span class="status-value" id="score">0</span>
                    </div>
                    <div class="status-item">
                        <span>🔋 Batteries Remaining:</span>
                        <span class="status-value" id="coins">8</span>
                    </div>
                    <div class="status-item">
                        <span>👣 Moves Made:</span>
                        <span class="status-value" id="moves">0</span>
                    </div>
                    <div class="status-item">
                        <span>🤖 Bot Position:</span>
                        <span class="status-value" id="bot-position">(2, 2)</span>
                    </div>
                    <div class="status-item">
                        <span>❤️ Bot Health:</span>
                        <span class="status-value" id="bot-health">3/3</span>
                    </div>
                    <div class="status-item">
                        <span>⚡ Laser Status:</span>
                        <span class="status-value" id="laser-status">Ready</span>
                    </div>
                </div>

                <div class="mcp-controls">
                    <h3>🤖 MCP Commands</h3>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('start_new_game')">🎮 New Game</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('look_around')">🔍 Look Around</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('analyze_threats')">🧠 Analyze</button>
                    <br>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('move_bot', {direction: 'north'})">⬆️ North</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('move_bot', {direction: 'south'})">⬇️ South</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('move_bot', {direction: 'east'})">➡️ East</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('move_bot', {direction: 'west'})">⬅️ West</button>
                    <br>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('fire_laser', {direction: 'north'})">🔥⬆️ Laser N</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('fire_laser', {direction: 'south'})">🔥⬇️ Laser S</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('fire_laser', {direction: 'east'})">🔥➡️ Laser E</button>
                    <button class="btn mcp-btn" onclick="sendMCPCommand('fire_laser', {direction: 'west'})">🔥⬅️ Laser W</button>
                </div>

                <div class="legend">
                    <h3>🗺️ Game Legend</h3>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #00ff88;">●</span>
                        <span>Your Bot (Green Circle)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #00c800;">●</span>
                        <span>Green Turtle (Random)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #ff0000;">●</span>
                        <span>Red Turtle (Chaser)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #0000ff;">●</span>
                        <span>Blue Turtle (Patrol)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #c8ff00;">▬</span>
                        <span>Battery (10 pts)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #ffd700;">▬</span>
                        <span>Golden Battery (50 pts)</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-emoji" style="color: #505050;">■</span>
                        <span>Wall</span>
                    </div>
                </div>
                
                <div class="game-log" id="game-log">
                    <div>🎮 Waiting for MCP connection...</div>
                </div>

                <div class="command-log" id="command-log">
                    <div>🤖 MCP Command Log:</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state and WebSocket connection
        let gameState = {
            grid: [],
            score: 0,
            coins: 8,
            moves: 0,
            bot_position: {x: 2, y: 2},
            bot_health: "3/3",
            laser_cooldown: 0,
            game_over: false,
            victory: false
        };

        let ws = null;
        const GRID_SIZE = 10;
        const CELL_SIZE = 50;

        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                updateConnectionStatus(true);
                addToLog('🔌 Connected to MCP Bridge Server');
                // Request initial game state
                sendMCPCommand('look_around');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            };
            
            ws.onclose = function() {
                updateConnectionStatus(false);
                addToLog('🔌 Disconnected from MCP Bridge Server');
                // Try to reconnect after 3 seconds
                setTimeout(initWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                addToLog('❌ WebSocket connection error');
            };
        }

        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            if (connected) {
                statusEl.className = 'connection-status connected';
                statusEl.textContent = '🟢 Connected to MCP Bridge';
            } else {
                statusEl.className = 'connection-status disconnected';
                statusEl.textContent = '🔴 Disconnected from MCP Bridge';
            }
        }

        function handleWebSocketMessage(message) {
            console.log("Received WebSocket message:", message);
            switch(message.type) {
                case 'game_state':
                case 'game_update':
                    console.log("Updating game state with:", message.data);
                    updateGameState(message.data);
                    if (message.data.command_result) {
                        addToCommandLog(message.data.command_result);
                    }
                    break;
                case 'error':
                    addToLog(`❌ Error: ${message.message}`);
                    break;
            }
        }

        function updateGameState(data) {
            console.log("updateGameState called with:", data);
            if (data.game_state) {
                console.log("Updating game state:", data.game_state);
                gameState = {...gameState, ...data.game_state};
                updateUI();
            }

            if (data.grid) {
                console.log("Updating grid:", data.grid);
                gameState.grid = data.grid;
                redraw(); // Trigger p5.js redraw
            }

            console.log("Current gameState after update:", gameState);
        }

        function updateUI() {
            document.getElementById('score').textContent = gameState.score || 0;
            document.getElementById('coins').textContent = gameState.batteries_remaining || 0;
            document.getElementById('moves').textContent = gameState.moves || 0;
            
            if (gameState.bot_position) {
                document.getElementById('bot-position').textContent = 
                    `(${gameState.bot_position.x}, ${gameState.bot_position.y})`;
            }
            
            document.getElementById('bot-health').textContent = gameState.bot_health || "3/3";
            
            const laserStatus = gameState.laser_cooldown > 0 ? 
                `Cooldown ${gameState.laser_cooldown}` : 'Ready';
            document.getElementById('laser-status').textContent = laserStatus;
        }

        function sendMCPCommand(command, params = {}) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'mcp_command',
                    command: command,
                    params: params
                };
                ws.send(JSON.stringify(message));
                addToCommandLog({status: 'sent', message: `Sent: ${command} ${JSON.stringify(params)}`});
            } else {
                addToLog('❌ Not connected to MCP Bridge');
            }
        }

        function addToLog(message) {
            const log = document.getElementById('game-log');
            const div = document.createElement('div');
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }

        function addToCommandLog(result) {
            const log = document.getElementById('command-log');
            const div = document.createElement('div');
            const status = result.status || 'unknown';
            const message = result.message || JSON.stringify(result);
            div.textContent = `${new Date().toLocaleTimeString()}: [${status.toUpperCase()}] ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }

        // P5.js functions
        function setup() {
            let canvas = createCanvas(GRID_SIZE * CELL_SIZE, GRID_SIZE * CELL_SIZE);
            canvas.parent('game-canvas-container');
            canvas.class('game-canvas');
        }

        function draw() {
            background(15, 15, 35);

            // Draw grid
            drawGrid();

            // Draw grid lines
            stroke(0, 212, 255, 80);
            strokeWeight(1);
            for (let i = 0; i <= GRID_SIZE; i++) {
                line(i * CELL_SIZE, 0, i * CELL_SIZE, height);
                line(0, i * CELL_SIZE, width, i * CELL_SIZE);
            }

            // Debug: Show grid state in console (remove this later)
            if (frameCount % 120 == 0) { // Every 2 seconds
                console.log("Grid state:", gameState.grid);
                console.log("Grid length:", gameState.grid ? gameState.grid.length : "undefined");
            }
        }

        function drawGrid() {
            // Debug: Check if we have grid data
            if (!gameState.grid || gameState.grid.length === 0) {
                // Draw a test pattern to show the grid is working
                fill(255, 0, 0, 100);
                textAlign(CENTER, CENTER);
                textSize(16);
                text("No Grid Data", width/2, height/2);
                return;
            }

            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    let cellX = x * CELL_SIZE;
                    let cellY = y * CELL_SIZE;
                    let centerX = cellX + CELL_SIZE / 2;
                    let centerY = cellY + CELL_SIZE / 2;

                    // Draw cell background
                    if (gameState.grid[y] && gameState.grid[y][x]) {
                        let cell = gameState.grid[y][x];

                        if (cell === '⬛') {
                            // Wall - dark gray
                            fill(50, 50, 50);
                            stroke(80, 80, 80);
                            strokeWeight(1);
                            rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                        } else {
                            // Empty space - blue tint
                            fill(70, 130, 180, 50);
                            noStroke();
                            rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                        }

                        // Draw game elements with shapes instead of emojis
                        if (cell !== '⬜' && cell !== '⬛') {
                            if (cell === '🤖') {
                                // Bot - bright green circle with glow
                                fill(0, 255, 136, 80);
                                noStroke();
                                circle(centerX, centerY, 45);

                                fill(0, 255, 136);
                                stroke(255, 255, 255);
                                strokeWeight(2);
                                circle(centerX, centerY, 30);

                                // Bot "eyes"
                                fill(255);
                                noStroke();
                                circle(centerX - 6, centerY - 4, 4);
                                circle(centerX + 6, centerY - 4, 4);

                            } else if (cell === '🐢') {
                                // Green turtle - green oval
                                fill(0, 200, 0, 100);
                                noStroke();
                                circle(centerX, centerY, 40);

                                fill(0, 150, 0);
                                stroke(0, 100, 0);
                                strokeWeight(2);
                                ellipse(centerX, centerY, 25, 20);

                            } else if (cell === '🔴') {
                                // Red turtle - red circle
                                fill(255, 0, 0, 100);
                                noStroke();
                                circle(centerX, centerY, 40);

                                fill(200, 0, 0);
                                stroke(150, 0, 0);
                                strokeWeight(2);
                                circle(centerX, centerY, 22);

                            } else if (cell === '🔵') {
                                // Blue turtle - blue circle
                                fill(0, 0, 255, 100);
                                noStroke();
                                circle(centerX, centerY, 40);

                                fill(0, 0, 200);
                                stroke(0, 0, 150);
                                strokeWeight(2);
                                circle(centerX, centerY, 22);

                            } else if (cell === '🔋') {
                                // Battery - yellow/green rectangle with glow
                                let glowIntensity = 30 + sin(frameCount * 0.1) * 20;
                                fill(255, 255, 0, glowIntensity);
                                noStroke();
                                circle(centerX, centerY, 35);

                                fill(200, 255, 0);
                                stroke(150, 200, 0);
                                strokeWeight(2);
                                rect(centerX - 8, centerY - 12, 16, 20, 2);

                                // Battery top
                                fill(150, 200, 0);
                                noStroke();
                                rect(centerX - 4, centerY - 14, 8, 3);

                            } else if (cell === '🏆') {
                                // Golden battery - golden rectangle with sparkle
                                let glowIntensity = 50 + sin(frameCount * 0.15) * 30;
                                fill(255, 215, 0, glowIntensity);
                                noStroke();
                                circle(centerX, centerY, 40);

                                fill(255, 215, 0);
                                stroke(255, 165, 0);
                                strokeWeight(2);
                                rect(centerX - 10, centerY - 15, 20, 25, 3);

                                // Golden top
                                fill(255, 165, 0);
                                noStroke();
                                rect(centerX - 6, centerY - 18, 12, 4);

                                // Sparkle effect
                                if (frameCount % 60 < 30) {
                                    fill(255, 255, 255, 200);
                                    noStroke();
                                    circle(centerX + 8, centerY - 8, 3);
                                    circle(centerX - 8, centerY + 8, 2);
                                }

                            } else if (cell === '💀') {
                                // Dead bot - red X
                                stroke(255, 0, 0);
                                strokeWeight(4);
                                line(centerX - 10, centerY - 10, centerX + 10, centerY + 10);
                                line(centerX + 10, centerY - 10, centerX - 10, centerY + 10);

                                fill(100, 0, 0, 150);
                                noStroke();
                                circle(centerX, centerY, 35);
                            }
                        }
                    } else {
                        // Default empty space
                        fill(70, 130, 180, 50);
                        noStroke();
                        rect(cellX, cellY, CELL_SIZE, CELL_SIZE);
                    }
                }
            }
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            initWebSocket();
            addToLog('🎮 Grid Bot Game with MCP Bridge loaded');
        });
    </script>
</body>
</html>
