#!/usr/bin/env python3
"""
Web Bridge Server for Grid Bot Game MCP Integration

This server creates a bridge between the MCP server and the web game,
allowing you to see the game being controlled by MCP commands in real-time.
"""

import asyncio
import json
import logging
from typing import Dict, Any
import websockets
from websockets.server import WebSocketServerProtocol
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import threading
import time

from game_api import GridBotGameAPI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global game API instance
game_api = GridBotGameAPI()

# Store connected WebSocket clients
connected_clients = set()

app = FastAPI(title="Grid Bot Game Web Bridge")

# Serve static files (HTML, CSS, JS)
app.mount("/static", StaticFiles(directory="."), name="static")


@app.get("/")
async def get_game():
    """Serve the main game page."""
    try:
        with open("game_with_websocket.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content)
    except FileNotFoundError:
        return HTMLResponse("<h1>Error: game_with_websocket.html not found</h1>", status_code=404)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time game updates."""
    await websocket.accept()
    connected_clients.add(websocket)
    logger.info("Client connected")
    
    try:
        # Send initial game state
        initial_state = game_api._get_game_status()
        grid_view = game_api.get_grid_view()
        
        await websocket.send_text(json.dumps({
            "type": "game_state",
            "data": {
                "status": initial_state,
                "grid": grid_view["grid"] if grid_view["status"] == "success" else []
            }
        }))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message["type"] == "mcp_command":
                    result = await handle_mcp_command(message["command"], message.get("params", {}))
                    await broadcast_game_update(result)
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
                
    except WebSocketDisconnect:
        pass
    finally:
        connected_clients.discard(websocket)
        logger.info("Client disconnected")


async def handle_mcp_command(command: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle MCP commands and return results."""
    try:
        if command == "start_new_game":
            result = game_api.initialize_game()
        elif command == "move_bot":
            direction = params.get("direction", "north")
            result = game_api.move_bot(direction)
        elif command == "look_around":
            result = game_api.look_around()
        elif command == "analyze_threats":
            result = game_api.analyze_threats()
        elif command == "fire_laser":
            direction = params.get("direction", "north")
            result = game_api.fire_laser(direction)
        elif command == "get_grid_view":
            result = game_api.get_grid_view()
        elif command == "get_game_status":
            result = {"status": "success", "game_state": game_api._get_game_status()}
        else:
            result = {"status": "error", "message": f"Unknown command: {command}"}
            
        return result
        
    except Exception as e:
        logger.error(f"Error executing MCP command {command}: {e}")
        return {"status": "error", "message": str(e)}


async def broadcast_game_update(command_result: Dict[str, Any]):
    """Broadcast game state updates to all connected clients."""
    if not connected_clients:
        return
        
    try:
        # Get current game state
        game_state = game_api._get_game_status()
        grid_view = game_api.get_grid_view()
        
        update_message = {
            "type": "game_update",
            "data": {
                "command_result": command_result,
                "game_state": game_state,
                "grid": grid_view["grid"] if grid_view["status"] == "success" else []
            }
        }
        
        # Send to all connected clients
        disconnected_clients = set()
        for client in connected_clients:
            try:
                await client.send_text(json.dumps(update_message))
            except Exception as e:
                logger.error(f"Error sending to client: {e}")
                disconnected_clients.add(client)
        
        # Remove disconnected clients
        connected_clients.difference_update(disconnected_clients)
        
    except Exception as e:
        logger.error(f"Error broadcasting game update: {e}")


@app.post("/api/mcp/{command}")
async def api_mcp_command(command: str, params: Dict[str, Any] = None):
    """REST API endpoint for MCP commands."""
    if params is None:
        params = {}
        
    result = await handle_mcp_command(command, params)
    
    # Broadcast update to WebSocket clients
    await broadcast_game_update(result)
    
    return result


class MCPCommandInterface:
    """Interface for sending MCP commands programmatically."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
    
    async def send_command(self, command: str, params: Dict[str, Any] = None):
        """Send an MCP command to the web bridge."""
        import httpx
        
        if params is None:
            params = {}
            
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{self.base_url}/api/mcp/{command}", json=params)
            return response.json()


def run_demo_sequence():
    """Run a demo sequence of MCP commands to show the game in action."""
    async def demo():
        interface = MCPCommandInterface()
        
        print("🎮 Starting MCP Demo Sequence...")
        
        # Wait for server to start
        await asyncio.sleep(2)
        
        commands = [
            ("start_new_game", {}),
            ("look_around", {}),
            ("analyze_threats", {}),
            ("move_bot", {"direction": "east"}),
            ("move_bot", {"direction": "south"}),
            ("look_around", {}),
            ("fire_laser", {"direction": "east"}),
            ("move_bot", {"direction": "west"}),
            ("get_game_status", {}),
        ]
        
        for command, params in commands:
            print(f"🔧 Executing: {command} {params}")
            try:
                result = await interface.send_command(command, params)
                print(f"   Result: {result.get('status', 'unknown')}")
                if 'message' in result:
                    print(f"   Message: {result['message']}")
                await asyncio.sleep(2)  # Pause between commands
            except Exception as e:
                print(f"   Error: {e}")
    
    # Run demo in background
    def run_demo():
        asyncio.run(demo())
    
    demo_thread = threading.Thread(target=run_demo, daemon=True)
    demo_thread.start()


if __name__ == "__main__":
    print("🌐 Starting Grid Bot Game Web Bridge Server")
    print("=" * 60)
    print("🎮 Game will be available at: http://localhost:8000")
    print("🔧 MCP API available at: http://localhost:8000/api/mcp/")
    print("🔌 WebSocket endpoint: ws://localhost:8000/ws")
    print("=" * 60)
    
    # Start demo sequence
    run_demo_sequence()
    
    # Start the web server
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
