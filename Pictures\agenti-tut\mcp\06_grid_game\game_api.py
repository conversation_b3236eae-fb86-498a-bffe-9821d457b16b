"""
Grid Bot Game API Layer
Provides a Python interface to control the HTML-based grid bot game.
"""

import json
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


class Direction(Enum):
    NORTH = "north"
    SOUTH = "south"
    EAST = "east"
    WEST = "west"


@dataclass
class BotState:
    x: int
    y: int
    alive: bool
    hits: int
    max_hits: int
    laser_cooldown: int


@dataclass
class GameState:
    grid: List[List[str]]
    bot: BotState
    score: int
    coins: int  # Actually batteries in the game
    moves: int
    turn: int
    game_over: bool
    victory: bool
    grid_size: int = 10


class GridBotGameAPI:
    """
    API layer for controlling the Grid Bot Game.
    This class maintains the game state and provides methods to control the bot.
    """
    
    def __init__(self):
        self.game_state: Optional[GameState] = None
        self.initialize_game()
    
    def initialize_game(self) -> Dict[str, Any]:
        """Initialize a new game and return the initial state."""
        grid_size = 10
        
        # Initialize empty grid with walls on borders
        grid = []
        for y in range(grid_size):
            row = []
            for x in range(grid_size):
                if x == 0 or x == grid_size-1 or y == 0 or y == grid_size-1:
                    row.append('⬛')  # Wall
                else:
                    row.append('⬜')  # Empty space
            grid.append(row)
        
        # Add some random internal walls
        import random
        for _ in range(6):
            x = random.randint(2, grid_size-3)
            y = random.randint(2, grid_size-3)
            grid[y][x] = '⬛'
        
        # Place bot at starting position
        bot = BotState(x=2, y=2, alive=True, hits=0, max_hits=3, laser_cooldown=0)
        grid[2][2] = '🤖'
        
        # Place enemies
        grid[7][7] = '🐢'  # Green turtle (random movement)
        grid[3][8] = '🔴'  # Red turtle (chaser)
        grid[8][3] = '🔵'  # Blue turtle (patrol)
        
        # Place batteries (called coins in code)
        battery_positions = [
            (4, 4), (6, 2), (2, 6), (5, 7), (7, 4), (3, 3), (6, 6), (1, 8)
        ]
        
        batteries_placed = 0
        for i, (x, y) in enumerate(battery_positions):
            if (x > 0 and x < grid_size-1 and y > 0 and y < grid_size-1 and 
                grid[y][x] == '⬜'):
                # First 2 are golden batteries, rest are regular
                grid[y][x] = '🏆' if i < 2 else '🔋'
                batteries_placed += 1
        
        # Add more batteries if needed
        while batteries_placed < 8:
            x = random.randint(2, grid_size-3)
            y = random.randint(2, grid_size-3)
            if grid[y][x] == '⬜':
                grid[y][x] = '🏆' if batteries_placed < 2 else '🔋'
                batteries_placed += 1
        
        # Initialize game state
        self.game_state = GameState(
            grid=grid,
            bot=bot,
            score=0,
            coins=8,  # Total batteries to collect
            moves=0,
            turn=0,
            game_over=False,
            victory=False,
            grid_size=grid_size
        )
        
        return {
            "status": "success",
            "message": "New game initialized",
            "game_state": self._get_game_status()
        }
    
    def move_bot(self, direction: str) -> Dict[str, Any]:
        """Move the bot in the specified direction."""
        if not self.game_state or self.game_state.game_over:
            return {
                "status": "error",
                "message": "Game is over or not initialized. Start a new game."
            }
        
        try:
            dir_enum = Direction(direction.lower())
        except ValueError:
            return {
                "status": "error",
                "message": f"Invalid direction: {direction}. Use north, south, east, or west."
            }
        
        bot = self.game_state.bot
        new_x, new_y = bot.x, bot.y
        
        # Calculate new position
        if dir_enum == Direction.NORTH:
            new_y -= 1
        elif dir_enum == Direction.SOUTH:
            new_y += 1
        elif dir_enum == Direction.EAST:
            new_x += 1
        elif dir_enum == Direction.WEST:
            new_x -= 1
        
        # Check bounds and walls
        if (new_x < 0 or new_x >= self.game_state.grid_size or 
            new_y < 0 or new_y >= self.game_state.grid_size or
            self.game_state.grid[new_y][new_x] == '⬛'):
            return {
                "status": "error",
                "message": f"Cannot move {direction} - blocked by wall or boundary!"
            }
        
        # Check what's at the target position
        cell_content = self.game_state.grid[new_y][new_x]
        
        # Clear old position
        self.game_state.grid[bot.y][bot.x] = '⬜'
        
        messages = []
        
        # Handle enemy collision
        if cell_content in ['🐢', '🔴', '🔵']:
            bot.hits += 1
            remaining_health = bot.max_hits - bot.hits
            messages.append(f"💥 Hit by enemy {cell_content}! Health: {remaining_health}/{bot.max_hits}")
            
            if bot.hits >= bot.max_hits:
                self.game_state.game_over = True
                bot.alive = False
                bot.x, bot.y = new_x, new_y
                self.game_state.grid[new_y][new_x] = '💀'
                messages.append("💀 GAME OVER! Bot destroyed after taking 3 hits!")
                return {
                    "status": "game_over",
                    "message": " ".join(messages),
                    "game_state": self._get_game_status()
                }
        
        # Handle battery collection
        elif cell_content == '🔋':
            self.game_state.score += 10
            self.game_state.coins -= 1
            messages.append("🔋 Collected battery! +10 points")
        elif cell_content == '🏆':
            self.game_state.score += 50
            self.game_state.coins -= 1
            messages.append("🏆 Collected golden battery! +50 points")
        
        # Move bot to new position
        bot.x, bot.y = new_x, new_y
        self.game_state.grid[new_y][new_x] = '🤖'
        self.game_state.moves += 1
        self.game_state.turn += 1
        
        # Reduce laser cooldown
        if bot.laser_cooldown > 0:
            bot.laser_cooldown -= 1
        
        # Check victory condition
        if self.game_state.coins == 0:
            self.game_state.game_over = True
            self.game_state.victory = True
            messages.append("🎉 VICTORY! All batteries collected!")
            messages.append(f"🏆 Final Score: {self.game_state.score} points in {self.game_state.moves} moves!")
        
        messages.append(f"🤖 Bot moved {direction} to ({new_x}, {new_y})")
        
        return {
            "status": "success" if not self.game_state.game_over else "victory",
            "message": " ".join(messages),
            "game_state": self._get_game_status()
        }
    
    def look_around(self) -> Dict[str, Any]:
        """Look around the bot's current position."""
        if not self.game_state:
            return {"status": "error", "message": "Game not initialized"}
        
        bot = self.game_state.bot
        surroundings = {}
        directions = [
            ('North', 0, -1), ('South', 0, 1), 
            ('East', 1, 0), ('West', -1, 0)
        ]
        
        for dir_name, dx, dy in directions:
            x = bot.x + dx
            y = bot.y + dy
            if (0 <= x < self.game_state.grid_size and 
                0 <= y < self.game_state.grid_size):
                surroundings[dir_name.lower()] = self.game_state.grid[y][x]
            else:
                surroundings[dir_name.lower()] = "boundary"
        
        return {
            "status": "success",
            "message": "Looking around current position",
            "surroundings": surroundings,
            "bot_position": {"x": bot.x, "y": bot.y}
        }
    
    def analyze_threats(self) -> Dict[str, Any]:
        """Analyze threats and opportunities on the grid."""
        if not self.game_state:
            return {"status": "error", "message": "Game not initialized"}
        
        bot = self.game_state.bot
        threats = []
        batteries = []
        
        for y in range(self.game_state.grid_size):
            for x in range(self.game_state.grid_size):
                cell = self.game_state.grid[y][x]
                distance = abs(x - bot.x) + abs(y - bot.y)  # Manhattan distance
                
                if cell in ['🐢', '🔴', '🔵']:
                    threats.append({
                        "type": cell,
                        "position": {"x": x, "y": y},
                        "distance": distance
                    })
                elif cell in ['🔋', '🏆']:
                    batteries.append({
                        "type": cell,
                        "position": {"x": x, "y": y},
                        "distance": distance,
                        "value": 50 if cell == '🏆' else 10
                    })
        
        # Sort by distance
        threats.sort(key=lambda t: t["distance"])
        batteries.sort(key=lambda b: b["distance"])
        
        return {
            "status": "success",
            "message": "Threat analysis complete",
            "threats": threats,
            "batteries": batteries,
            "bot_health": f"{bot.max_hits - bot.hits}/{bot.max_hits}"
        }
    
    def fire_laser(self, direction: str) -> Dict[str, Any]:
        """Fire laser in the specified direction."""
        if not self.game_state or self.game_state.game_over:
            return {
                "status": "error",
                "message": "Game is over or not initialized. Start a new game."
            }

        if self.game_state.bot.laser_cooldown > 0:
            return {
                "status": "error",
                "message": f"Laser cooling down: {self.game_state.bot.laser_cooldown} turns remaining"
            }

        try:
            dir_enum = Direction(direction.lower())
        except ValueError:
            return {
                "status": "error",
                "message": f"Invalid direction: {direction}. Use north, south, east, or west."
            }

        bot = self.game_state.bot
        dx, dy = 0, 0

        if dir_enum == Direction.NORTH:
            dy = -1
        elif dir_enum == Direction.SOUTH:
            dy = 1
        elif dir_enum == Direction.EAST:
            dx = 1
        elif dir_enum == Direction.WEST:
            dx = -1

        # Find first target in laser path
        current_x = bot.x + dx
        current_y = bot.y + dy
        hit = False
        messages = []

        while (0 <= current_x < self.game_state.grid_size and
               0 <= current_y < self.game_state.grid_size):

            cell = self.game_state.grid[current_y][current_x]

            # Hit wall - laser stops
            if cell == '⬛':
                break

            # Hit enemy - destroy it
            if cell in ['🐢', '🔴', '🔵']:
                self.game_state.grid[current_y][current_x] = '⬜'
                messages.append(f"🔥 Laser destroyed enemy {cell} at ({current_x}, {current_y})!")
                hit = True
                break

            # Hit battery - destroy it (oops!)
            if cell in ['🔋', '🏆']:
                self.game_state.grid[current_y][current_x] = '⬜'
                self.game_state.coins -= 1
                battery_type = "golden battery 🏆" if cell == '🏆' else "regular battery 🔋"
                messages.append(f"💥 Laser accidentally destroyed {battery_type} at ({current_x}, {current_y})!")
                hit = True
                break

            current_x += dx
            current_y += dy

        # Set cooldown
        self.game_state.bot.laser_cooldown = 3

        if not hit:
            messages.append(f"🔥 Laser fired {direction} - no targets hit")

        return {
            "status": "success",
            "message": " ".join(messages),
            "game_state": self._get_game_status()
        }

    def get_grid_view(self) -> Dict[str, Any]:
        """Get a visual representation of the current grid."""
        if not self.game_state:
            return {"status": "error", "message": "Game not initialized"}

        grid_view = []
        for row in self.game_state.grid:
            grid_view.append("".join(row))

        return {
            "status": "success",
            "grid": grid_view,
            "legend": {
                "🤖": "Your Bot",
                "🐢": "Green Turtle (Random)",
                "🔴": "Red Turtle (Chaser)",
                "🔵": "Blue Turtle (Patrol)",
                "🔋": "Battery (10 pts)",
                "🏆": "Golden Battery (50 pts)",
                "⬛": "Wall",
                "⬜": "Empty Space",
                "💀": "Dead Bot"
            }
        }

    def _get_game_status(self) -> Dict[str, Any]:
        """Get current game status."""
        if not self.game_state:
            return {}

        return {
            "score": self.game_state.score,
            "batteries_remaining": self.game_state.coins,
            "moves": self.game_state.moves,
            "turn": self.game_state.turn,
            "bot_position": {"x": self.game_state.bot.x, "y": self.game_state.bot.y},
            "bot_health": f"{self.game_state.bot.max_hits - self.game_state.bot.hits}/{self.game_state.bot.max_hits}",
            "laser_cooldown": self.game_state.bot.laser_cooldown,
            "game_over": self.game_state.game_over,
            "victory": self.game_state.victory
        }
